<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- Success Result -->
      <div v-if="insuranceStore.paymentStatus === 'success'" class="text-center">
        <!-- Success Icon -->
        <div class="mb-8">
          <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Mu<PERSON> bảo hiểm thành công!</h1>
          <p class="text-gray-600">Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi</p>
        </div>

        <!-- Order Information -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6 text-left">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin đơn hàng</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Mã đơn hàng</label>
              <p class="text-gray-900 font-medium">#{{ insuranceStore.orderId }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Ngày mua</label>
              <p class="text-gray-900 font-medium">{{ currentDate }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Biển số xe</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Tên công ty</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Thời hạn bảo hiểm</label>
              <p class="text-gray-900 font-medium">
                {{ insuranceStore.formattedStartDate }} - {{ insuranceStore.formattedEndDate }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Số tiền đã thanh toán</label>
              <p class="text-green-600 font-bold text-lg">{{ insuranceStore.formattedFees.totalPayment }}</p>
            </div>
          </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 rounded-lg p-6 mb-6 text-left">
          <h3 class="text-lg font-semibold text-blue-900 mb-4">Các bước tiếp theo</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">1</div>
              <p class="text-blue-800">
                Giấy chứng nhận bảo hiểm sẽ được gửi đến email: 
                <span class="font-medium">{{ insuranceStore.ownerInfo.email_gcn }}</span>
              </p>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">2</div>
              <p class="text-blue-800">Thời gian xử lý: trong vòng 24 giờ làm việc</p>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">3</div>
              <p class="text-blue-800">Vui lòng kiểm tra email và lưu giữ giấy chứng nhận</p>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6 text-left">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Thông tin liên hệ hỗ trợ</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Hotline</label>
              <p class="text-gray-900 font-medium">1900 1234</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Email hỗ trợ</label>
              <p class="text-gray-900 font-medium"><EMAIL></p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Giờ làm việc</label>
              <p class="text-gray-900 font-medium">8:00 - 17:00 (Thứ 2 - Thứ 6)</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Website</label>
              <p class="text-gray-900 font-medium">www.vivas.vn</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Failed Result -->
      <div v-else class="text-center">
        <!-- Failed Icon -->
        <div class="mb-8">
          <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Mua bảo hiểm không thành công</h1>
          <p class="text-gray-600">Đã có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại.</p>
        </div>

        <!-- Error Information -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6 text-left">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin lỗi</h2>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <svg class="w-5 h-5 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h3 class="text-red-800 font-medium">Giao dịch không thành công</h3>
                <p class="text-red-700 mt-1">
                  Có thể do: Thanh toán không thành công, hết thời gian giao dịch, hoặc lỗi hệ thống.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Retry Options -->
        <div class="bg-blue-50 rounded-lg p-6 mb-6 text-left">
          <h3 class="text-lg font-semibold text-blue-900 mb-4">Bạn có thể thực hiện</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">1</div>
              <p class="text-blue-800">Thử lại quá trình mua bảo hiểm từ đầu</p>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">2</div>
              <p class="text-blue-800">Liên hệ hotline 1900 1234 để được hỗ trợ</p>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">3</div>
              <p class="text-blue-800">Gửi email đến <EMAIL> với thông tin chi tiết</p>
            </div>
          </div>
        </div>

        <!-- Order Information (if available) -->
        <div v-if="insuranceStore.orderId" class="bg-white rounded-lg shadow-sm p-6 mb-6 text-left">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin đơn hàng</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Mã đơn hàng</label>
              <p class="text-gray-900 font-medium">#{{ insuranceStore.orderId }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Thời gian</label>
              <p class="text-gray-900 font-medium">{{ currentDate }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Biển số xe</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Trạng thái</label>
              <p class="text-red-600 font-medium">Không thành công</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-center space-x-4">
        <button
          v-if="insuranceStore.paymentStatus !== 'success'"
          type="button"
          @click="retryPurchase"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Thử lại
        </button>
        <button
          type="button"
          @click="goToHome"
          class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Quay lại trang chủ
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Kết quả mua bảo hiểm'
})

const insuranceStore = useInsuranceStore()

// Kiểm tra nếu chưa có thông tin thanh toán
onMounted(() => {
  if (!insuranceStore.orderId) {
    navigateTo('/insurance/step1')
  }
})

// Lấy ngày hiện tại
const currentDate = computed(() => {
  return new Date().toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
})

// Thử lại quá trình mua bảo hiểm
const retryPurchase = () => {
  // Reset store và quay về bước 1
  insuranceStore.resetStore()
  navigateTo('/insurance/step1')
}

// Quay về trang chủ
const goToHome = () => {
  // Reset store
  insuranceStore.resetStore()
  navigateTo('/')
}
</script>
