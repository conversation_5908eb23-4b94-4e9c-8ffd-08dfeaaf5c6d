<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4"><PERSON><PERSON> bả<PERSON> hiểm trự<PERSON> tuyến</h1>
        <p class="text-gray-600 text-lg">
          <PERSON><PERSON><PERSON> hiể<PERSON> b<PERSON><PERSON> bu<PERSON><PERSON> tr<PERSON>ch nhiệm dân sự của chủ xe ô tô
        </p>
      </div>

      <!-- Process Overview -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Quy trình mua bảo hiểm</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Step 1 -->
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Khai báo thông tin</h3>
            <p class="text-gray-600 text-sm">Nhập thông tin chủ xe và thông tin xe cần mua bảo hiểm</p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Xác nhận thông tin</h3>
            <p class="text-gray-600 text-sm">Kiểm tra lại thông tin và phí bảo hiểm trước khi thanh toán</p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Thanh toán</h3>
            <p class="text-gray-600 text-sm">Thanh toán qua QR code hoặc chuyển khoản ngân hàng</p>
          </div>
        </div>
      </div>

      <!-- Insurance Information -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Thông tin bảo hiểm</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-semibold text-gray-900 mb-3">Phạm vi bảo hiểm</h3>
            <ul class="space-y-2 text-gray-700">
              <li class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Thiệt hại về người: tối đa 150 triệu đồng/người</span>
              </li>
              <li class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Thiệt hại về tài sản: tối đa 50 triệu đồng/vụ</span>
              </li>
              <li class="flex items-start space-x-2">
                <svg class="w-5 h-5 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Thời hạn bảo hiểm: 30 ngày</span>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 mb-3">Phí bảo hiểm</h3>
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-700">Phí chưa VAT:</span>
                  <span class="font-medium">266.666đ</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-700">Thuế VAT (10%):</span>
                  <span class="font-medium">26.667đ</span>
                </div>
                <div class="flex justify-between border-t pt-2">
                  <span class="font-semibold text-gray-900">Tổng phí:</span>
                  <span class="font-bold text-blue-600 text-lg">293.333đ</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Requirements -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <h3 class="font-semibold text-yellow-800 mb-3">Lưu ý quan trọng</h3>
        <ul class="space-y-2 text-yellow-700">
          <li class="flex items-start space-x-2">
            <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>Bảo hiểm bắt buộc theo quy định của pháp luật Việt Nam</span>
          </li>
          <li class="flex items-start space-x-2">
            <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>Vui lòng chuẩn bị đầy đủ thông tin xe và chủ xe</span>
          </li>
          <li class="flex items-start space-x-2">
            <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>Giấy chứng nhận sẽ được gửi qua email trong vòng 24h</span>
          </li>
        </ul>
      </div>

      <!-- Start Button -->
      <div class="text-center">
        <button
          @click="startInsurancePurchase"
          :disabled="!authStore.isLoggedIn"
          class="px-8 py-3 bg-blue-600 text-white text-lg font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="authStore.isLoggedIn">Bắt đầu mua bảo hiểm</span>
          <span v-else>Vui lòng đăng nhập để tiếp tục</span>
        </button>
        
        <div v-if="!authStore.isLoggedIn" class="mt-4">
          <p class="text-gray-600 mb-2">Bạn chưa có tài khoản?</p>
          <button
            @click="openLoginModal"
            class="text-blue-600 hover:text-blue-800 font-medium"
          >
            Đăng nhập ngay
          </button>
        </div>
      </div>

      <!-- Contact Support -->
      <div class="mt-12 text-center">
        <h3 class="font-semibold text-gray-900 mb-4">Cần hỗ trợ?</h3>
        <div class="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span class="text-gray-700">Hotline: 1900 1234</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span class="text-gray-700">Email: <EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Mua bảo hiểm trực tuyến - VIVAS'
})

const authStore = useAuthStore()
const insuranceStore = useInsuranceStore()

// Bắt đầu quy trình mua bảo hiểm
const startInsurancePurchase = () => {
  if (!authStore.isLoggedIn) {
    return
  }
  
  // Reset store và khởi tạo thông tin từ user
  insuranceStore.resetStore()
  insuranceStore.initializeFromUser()
  
  // Chuyển đến bước 1
  navigateTo('/insurance/step1')
}

// Mở modal đăng nhập (nếu có)
const openLoginModal = () => {
  // Trigger login modal - cần implement tùy theo cách bạn quản lý modal
  // Ví dụ: emit event hoặc gọi store method
  console.log('Open login modal')
}
</script>
