<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON> b<PERSON>o hiểm tr<PERSON><PERSON> tuyến</h1>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <span class="ml-2 text-green-600 font-medium">Khai báo thông tin</span>
          </div>
          <div class="w-8 h-px bg-green-600"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
            <span class="ml-2 text-blue-600 font-medium"><PERSON><PERSON><PERSON> nhận thông tin</span>
          </div>
          <div class="w-8 h-px bg-gray-300"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
            <span class="ml-2 text-gray-500">Thanh toán</span>
          </div>
        </div>
      </div>

      <!-- Thông tin xác nhận -->
      <div class="space-y-6">
        <!-- Thông tin chủ xe -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin chủ xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Tên công ty</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Mã số thuế</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.tax_number }}</p>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-500">Địa chỉ công ty</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_address }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Số điện thoại</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.phone_number }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Email nhận GCN</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.email_gcn }}</p>
            </div>
          </div>
        </div>

        <!-- Thông tin xe -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Biển kiểm soát</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Số chỗ ngồi</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_cho_ngoi }}</p>
            </div>
            <div v-if="insuranceStore.vehicleInfo.so_khung">
              <label class="block text-sm font-medium text-gray-500">Số khung</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_khung }}</p>
            </div>
            <div v-if="insuranceStore.vehicleInfo.so_may">
              <label class="block text-sm font-medium text-gray-500">Số máy</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.so_may }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Trọng tải</label>
              <p class="text-gray-900 font-medium">Trên 15 tấn</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Loại xe</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.loai_xe }}</p>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-500">Mục đích sử dụng</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.md_su_dung }}</p>
            </div>
          </div>
        </div>

        <!-- Thời hạn bảo hiểm -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thời hạn bảo hiểm</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Ngày bắt đầu</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.formattedStartDate }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Ngày kết thúc</label>
              <p class="text-gray-900 font-medium">{{ insuranceStore.formattedEndDate }}</p>
            </div>
          </div>
        </div>

        <!-- Phí bảo hiểm -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Phí bảo hiểm</h2>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-gray-700">Phí chưa VAT:</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.feeWithoutVat }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700">Thuế VAT (10%):</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.vatAmount }}</span>
            </div>
            <div class="flex justify-between items-center border-t pt-3">
              <span class="text-gray-700">Tổng phí (gồm VAT):</span>
              <span class="font-medium">{{ insuranceStore.formattedFees.totalFeeWithVat }}</span>
            </div>
            <div class="flex justify-between items-center border-t pt-3">
              <span class="text-lg font-semibold text-gray-900">TỔNG PHÍ THANH TOÁN:</span>
              <span class="text-lg font-bold text-red-600">{{ insuranceStore.formattedFees.totalPayment }}</span>
            </div>
          </div>
        </div>

        <!-- Checkbox xác nhận -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-start space-x-3">
            <input
              type="checkbox"
              v-model="insuranceStore.isAgreed"
              class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
            />
            <div class="flex-1">
              <p class="text-gray-700">
                Tôi đồng ý đã đọc, hiểu các quy định Pháp luật về Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
                <a 
                  href="#" 
                  @click.prevent="openInsuranceRegulations"
                  class="text-blue-600 hover:text-blue-800 underline ml-1"
                >
                  (Xem quy định bảo hiểm)
                </a>
              </p>
            </div>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-between">
          <button
            type="button"
            @click="goBack"
            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Quay lại
          </button>
          <button
            type="button"
            @click="proceedToPayment"
            :disabled="!insuranceStore.isStep2Valid || isLoading"
            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading">Đang xử lý...</span>
            <span v-else>Thanh toán</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal quy định bảo hiểm -->
    <div v-if="showRegulationsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-2xl max-h-[80vh] overflow-y-auto p-6 m-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Quy định Bảo hiểm bắt buộc trách nhiệm dân sự</h3>
          <button
            @click="closeRegulationsModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="prose max-w-none">
          <p class="text-gray-700 mb-4">
            Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe cơ giới là loại bảo hiểm nhằm bảo vệ lợi ích của người thứ ba 
            bị thiệt hại do xe cơ giới gây ra.
          </p>
          <h4 class="font-semibold mb-2">Phạm vi bảo hiểm:</h4>
          <ul class="list-disc pl-5 mb-4">
            <li>Thiệt hại về người: tối đa 150 triệu đồng/người/vụ tai nạn</li>
            <li>Thiệt hại về tài sản: tối đa 50 triệu đồng/vụ tai nạn</li>
          </ul>
          <h4 class="font-semibold mb-2">Trách nhiệm của chủ xe:</h4>
          <ul class="list-disc pl-5 mb-4">
            <li>Mua bảo hiểm bắt buộc trước khi đưa xe vào sử dụng</li>
            <li>Duy trì hiệu lực bảo hiểm trong suốt thời gian sử dụng xe</li>
            <li>Thông báo kịp thời cho công ty bảo hiểm khi xảy ra tai nạn</li>
          </ul>
        </div>
        <div class="flex justify-end mt-6">
          <button
            @click="closeRegulationsModal"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Xác nhận thông tin mua bảo hiểm'
})

const insuranceStore = useInsuranceStore()
const isLoading = ref(false)
const showRegulationsModal = ref(false)

// Kiểm tra nếu chưa có thông tin từ bước 1
onMounted(() => {
  if (!insuranceStore.isStep1Valid) {
    navigateTo('/insurance/step1')
  }
})

// Mở modal quy định bảo hiểm
const openInsuranceRegulations = () => {
  showRegulationsModal.value = true
}

// Đóng modal quy định bảo hiểm
const closeRegulationsModal = () => {
  showRegulationsModal.value = false
}

// Quay lại bước 1
const goBack = () => {
  navigateTo('/insurance/step1')
}

// Tiến hành thanh toán
const proceedToPayment = async () => {
  if (!insuranceStore.isStep2Valid) {
    return
  }

  isLoading.value = true

  try {
    const { createInsuranceOrder } = useInsuranceApi()
    const result = await createInsuranceOrder()

    if (result.success) {
      // Chuyển đến bước thanh toán
      setTimeout(() => {
        navigateTo('/insurance/step3')
      }, 1500)
    }
  } catch (error) {
    console.error('Error creating order:', error)
  } finally {
    isLoading.value = false
  }
}
</script>
