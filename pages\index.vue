<template>
  <div class="bg-[#ffffff] relative size-full min-h-[100vh]" data-name="Trang chủ">

    <!-- Background decorative elements -->
    <div class="absolute contents left-[1109px] top-[-40px] overflow-hidden">
      <div class="absolute flex h-[588px] items-center justify-center left-[1717.77px] top-[-40px] w-[453.569px]">
        <div class="flex-none scale-y-[-100%]">
          <div class="h-[588px] relative w-[453.569px]">
            <img alt="" class="block max-w-none size-full" :src="imgRectangle30" />
          </div>
        </div>
      </div>
      <div class="absolute flex h-[588px] items-center justify-center left-[1109px] top-[-40px] w-[453.569px]">
        <div class="flex-none scale-y-[-100%]">
          <div class="h-[588px] relative w-[453.569px]">
            <img alt="" class="block max-w-none size-full" :src="imgRectangle27" />
          </div>
        </div>
      </div>
      <div class="absolute flex h-[588px] items-center justify-center left-[1527.07px] top-[-40px] w-[454.54px]">
        <div class="flex-none scale-y-[-100%]">
          <div class="h-[588px] relative w-[454.54px]">
            <img alt="" class="block max-w-none size-full" :src="imgRectangle29" />
          </div>
        </div>
      </div>
      <div class="absolute flex h-[588px] items-center justify-center left-[1299.39px] top-[-40px] w-[454.54px]">
        <div class="flex-none scale-y-[-100%]">
          <div class="h-[588px] relative w-[454.54px]">
            <img alt="" class="block max-w-none size-full" :src="imgRectangle28" />
          </div>
        </div>
      </div>
    </div>

    <!-- Main banner image -->
    <div class="absolute h-[548px] left-[1072.56px] top-[75px] w-[847.44px]" data-name="banner 1">
      <img alt="Banner bảo hiểm" class="block max-w-none size-full object-cover" :src="imgBanner1" />
    </div>

    <!-- Side images -->
    <div
      class="absolute box-border content-stretch flex flex-col gap-[25.021px] items-start justify-start left-[1072.56px] p-0 top-[648px] w-[367px]">
      <div class="flex flex-row items-center self-stretch">
        <div class="aspect-[367/150] bg-center bg-cover bg-no-repeat h-full shrink-0" data-name="image 1"
          :style="{ backgroundImage: `url('${imgImage1}')` }" />
      </div>
      <div class="bg-center bg-no-repeat h-[76.979px] shrink-0 w-[269.078px] bg-cover" data-name="image 2"
        :style="{ backgroundImage: `url('${imgImage2}')` }" />
    </div>

    <!-- Main content -->
    <div
      class="absolute box-border content-stretch flex flex-col gap-7 items-start justify-start left-[391px] p-0 top-[147px] w-[681.559px]">
      <!-- Title section -->
      <div
        class="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
        <div
          class="[grid-area:1_/_1] capitalize font-['Helvetica:Bold',_sans-serif] ml-0 mt-0 not-italic relative text-[#000000] text-[65.4297px] text-left tracking-[-3.27148px] w-[681.559px]">
          <p class="adjustLetterSpacing block font-['Helvetica_Neue:Bold',_sans-serif] leading-[normal] mb-0">
            Bảo hiểm Bắt buộc trách nhiệm dân sự
          </p>
          <p class="font-['Helvetica_Neue:Bold',_sans-serif] leading-[normal]">
            <span>của </span>
            <span class="adjustLetterSpacing text-[#0d68b2]">xe cơ giới </span>
          </p>
        </div>
        <div class="[grid-area:1_/_1] h-[16.106px] ml-[127.872px] mt-[210.894px] relative w-[322.098px]"
          data-name="Vector 7 (Stroke)">
          <img alt="" class="block max-w-none size-full" :src="imgVector7Stroke" />
        </div>
      </div>

      <!-- Features section -->
      <div
        class="box-border content-stretch flex flex-col gap-5 items-start justify-start p-0 relative shrink-0 w-[647px]">
        <!-- Time feature -->
        <div
          class="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0 w-full">
          <div class="relative shrink-0 size-5" data-name="icon_time 1">
            <img alt="Thời gian" class="block max-w-none size-full" :src="imgIconTime1" />
          </div>
          <div
            class="basis-0 font-['Helvetica_Neue:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[#000000] text-[20px] text-left text-nowrap">
            <p class="block leading-[1.2] overflow-inherit">
              Thời hạn bảo hiểm: 30 ngày.
            </p>
          </div>
        </div>

        <!-- Benefits feature -->
        <div
          class="box-border content-stretch flex flex-row gap-3 h-12 items-start justify-start p-0 relative shrink-0 w-full">
          <div class="overflow-clip relative shrink-0 size-5" data-name="icon_benefit 1">
            <div class="absolute contents inset-0" data-name="Clip path group">
              <div
                class="absolute bottom-[0.151%] left-[3.467%] mask-alpha mask-intersect mask-no-clip mask-no-repeat right-[3.271%] top-[0.037%]"
                data-name="Group" :style="{ maskImage: `url('${imgGroup}')` }">
                <img alt="Quyền lợi" class="block max-w-none size-full" :src="imgGroup1" />
              </div>
            </div>
          </div>
          <div
            class="basis-0 font-['Helvetica_Neue:Regular',_sans-serif] grow h-12 leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[#000000] text-[20px] text-left">
            <p class="block leading-[1.2]">
              Quyền lợi: Về người tối đa 150.000.000 đồng/người/vụ tai nạn, về
              tài sản tối đa 100.000.000 đồng/vụ tai nạn.
            </p>
          </div>
        </div>

        <!-- Price feature -->
        <div
          class="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full">
          <div class="relative shrink-0 size-5" data-name="icon_list 1">
            <img alt="Giá" class="block max-w-none size-full" :src="imgIconList1" />
          </div>
          <div
            class="basis-0 font-['Helvetica_Neue:Bold',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[#ee1c23] text-[28px] text-left text-nowrap">
            <p class="block leading-[1.2] overflow-inherit">
              Phí: 293,333 đ
            </p>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div
        class="box-border content-stretch flex flex-col gap-3 h-[171.105px] items-start justify-center p-0 relative shrink-0 w-[598px]">
        <div
          @click="goToInsurance"
          class="basis-0 bg-[#0d68b2] grow min-h-px min-w-px relative rounded-[14.6129px] shadow-[0px_10.9597px_36.5322px_0px_rgba(0,0,0,0.15)] shrink-0 w-full cursor-pointer hover:bg-[#0a5a9a] transition-colors">
          <div class="flex flex-row items-center justify-center relative size-full">
            <div
              class="box-border content-stretch flex flex-row gap-[11.69px] items-center justify-center px-[43.839px] py-[21.919px] relative size-full">
              <div
                class="font-['Helvetica_Neue:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#f6f6f6] text-[24px] text-center text-nowrap">
                <p class="block leading-[normal] whitespace-pre">
                  Mua bảo hiểm
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="basis-0 bg-[#ffffff] grow min-h-px min-w-px relative rounded-[14.6129px] shrink-0 w-full cursor-pointer hover:bg-[#f8f9fa] transition-colors">
          <div
            class="absolute border border-[#0d68b2] border-solid inset-0 pointer-events-none rounded-[14.6129px] shadow-[0px_10.9597px_36.5322px_0px_rgba(0,0,0,0.15)]" />
          <div class="flex flex-row items-center justify-center relative size-full">
            <div
              class="box-border content-stretch flex flex-row gap-[11.69px] items-center justify-center px-[43.839px] py-[21.919px] relative size-full">
              <div
                class="font-bold leading-[0] not-italic relative shrink-0 text-[#0d68b2] text-[24px] text-center w-[191px]">
                <p class="block leading-[normal]">Xem chi tiết</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Define image paths
const imgBanner1 = '/assets/images/banner1.png'
const imgImage1 = '/assets/images/image1.png'
const imgImage2 = '/assets/images/image2.png'
const imgLogoSvg = '/assets/images/logo.svg'
const imgRectangle30 = '/assets/images/rectangle30.svg'
const imgRectangle27 = '/assets/images/rectangle27.svg'
const imgRectangle29 = '/assets/images/rectangle29.svg'
const imgRectangle28 = '/assets/images/rectangle28.svg'
const imgVector7Stroke = '/assets/images/vector7-stroke.svg'
const imgIconTime1 = '/assets/images/icon-time.svg'

// Navigation function for insurance purchase
const goToInsurance = () => {
  navigateTo('/insurance')
}
const imgGroup = '/assets/images/group.svg'
const imgGroup1 = '/assets/images/group1.svg'
const imgIconList1 = '/assets/images/icon-list.svg'

// SEO Meta
useHead({
  title: 'Bảo hiểm BIC - Bảo hiểm Bắt buộc trách nhiệm dân sự xe cơ giới',
  meta: [
    {
      name: 'description',
      content: 'Bảo hiểm bắt buộc trách nhiệm dân sự của xe cơ giới với quyền lợi tối đa 150 triệu đồng/người và 100 triệu đồng/tài sản. Thời hạn 30 ngày, phí chỉ 293,333 đ.'
    },
    {
      property: 'og:title',
      content: 'Bảo hiểm BIC - Bảo hiểm Bắt buộc trách nhiệm dân sự xe cơ giới'
    },
    {
      property: 'og:description',
      content: 'Bảo hiểm bắt buộc trách nhiệm dân sự của xe cơ giới với quyền lợi tối đa 150 triệu đồng/người và 100 triệu đồng/tài sản.'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script>

<style scoped>
.adjustLetterSpacing {
  letter-spacing: -3.27148px;
}

/* Custom styles for better responsive behavior */
@media (max-width: 1920px) {
  .absolute[data-name="Container"] {
    left: 20px;
    right: 20px;
    max-width: calc(100% - 40px);
  }
}

@media (max-width: 1200px) {
  .absolute[data-name="Header"] {
    position: relative;
    width: 100%;
  }

  .absolute[data-name="Container"] {
    position: relative;
    left: 0;
    right: 0;
    max-width: 100%;
    padding: 0 20px;
  }

  .absolute[data-name="Background+HorizontalBorder"] {
    position: relative;
    bottom: auto;
    margin-top: 50px;
  }
}

/* Ensure proper font loading */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@400;700&display=swap');

/* Fallback for Helvetica Neue */
.font-helvetica-neue-regular {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
}

.font-helvetica-neue-bold {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 700;
}
</style>