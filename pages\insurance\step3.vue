<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON> b<PERSON>o hiểm tr<PERSON><PERSON> tuyến</h1>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <span class="ml-2 text-green-600 font-medium">Khai báo thông tin</span>
          </div>
          <div class="w-8 h-px bg-green-600"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
            <span class="ml-2 text-green-600 font-medium"><PERSON><PERSON><PERSON> nhận thông tin</span>
          </div>
          <div class="w-8 h-px bg-green-600"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
            <span class="ml-2 text-blue-600 font-medium">Thanh toán</span>
          </div>
        </div>
      </div>

      <!-- Thông tin đơn hàng -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin đơn hàng</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-500">Mã đơn hàng</label>
            <p class="text-gray-900 font-medium">#{{ insuranceStore.orderId }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Biển số xe</label>
            <p class="text-gray-900 font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Tên công ty</label>
            <p class="text-gray-900 font-medium">{{ insuranceStore.ownerInfo.company_name }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Số tiền thanh toán</label>
            <p class="text-red-600 font-bold text-lg">{{ insuranceStore.formattedFees.totalPayment }}</p>
          </div>
        </div>
      </div>

      <!-- Thanh toán QR Code -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Thanh toán bằng QR Code</h2>
        
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- QR Code -->
          <div class="flex-1 flex flex-col items-center">
            <div class="bg-gray-100 p-8 rounded-lg mb-4">
              <div v-if="qrCodeUrl" class="w-64 h-64 flex items-center justify-center">
                <img :src="qrCodeUrl" alt="QR Code thanh toán" class="max-w-full max-h-full" />
              </div>
              <div v-else class="w-64 h-64 flex items-center justify-center">
                <div class="text-center">
                  <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p class="text-gray-500">Đang tạo mã QR...</p>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 text-center">
              Quét mã QR bằng ứng dụng ngân hàng để thanh toán
            </p>
          </div>

          <!-- Hướng dẫn thanh toán -->
          <div class="flex-1">
            <h3 class="font-semibold text-gray-900 mb-4">Hướng dẫn thanh toán</h3>
            <div class="space-y-3">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">1</div>
                <p class="text-gray-700">Mở ứng dụng ngân hàng trên điện thoại của bạn</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">2</div>
                <p class="text-gray-700">Chọn chức năng "Quét QR" hoặc "Chuyển khoản QR"</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">3</div>
                <p class="text-gray-700">Quét mã QR hiển thị bên trái</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">4</div>
                <p class="text-gray-700">Xác nhận thông tin và thực hiện thanh toán</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">5</div>
                <p class="text-gray-700">Chờ hệ thống xác nhận thanh toán thành công</p>
              </div>
            </div>

            <!-- Thông tin chuyển khoản thủ công -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Hoặc chuyển khoản thủ công:</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Ngân hàng:</span>
                  <span class="font-medium">Vietcombank</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Số tài khoản:</span>
                  <span class="font-medium">**********</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Chủ tài khoản:</span>
                  <span class="font-medium">VIVAS INSURANCE</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Nội dung:</span>
                  <span class="font-medium">BH {{ insuranceStore.orderId }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Số tiền:</span>
                  <span class="font-medium text-red-600">{{ insuranceStore.formattedFees.totalPayment }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Trạng thái thanh toán -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 class="font-semibold text-gray-900 mb-4">Trạng thái thanh toán</h3>
        <div class="flex items-center space-x-3">
          <div v-if="paymentStatus === 'pending'" class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-blue-600 font-medium">Đang chờ thanh toán...</span>
          </div>
          <div v-else-if="paymentStatus === 'success'" class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-green-600 font-medium">Thanh toán thành công!</span>
          </div>
          <div v-else-if="paymentStatus === 'failed'" class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <span class="text-red-600 font-medium">Thanh toán thất bại!</span>
          </div>
        </div>
        
        <div class="mt-4 text-sm text-gray-600">
          <p v-if="paymentStatus === 'pending'">
            Vui lòng thực hiện thanh toán trong vòng <span class="font-medium text-red-600">{{ formatTime(timeRemaining) }}</span>
          </p>
          <p v-else-if="paymentStatus === 'success'">
            Giao dịch đã được xử lý thành công. Bạn sẽ được chuyển đến trang kết quả.
          </p>
          <p v-else-if="paymentStatus === 'failed'">
            Giao dịch không thành công. Vui lòng thử lại hoặc liên hệ hỗ trợ.
          </p>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-between">
        <button
          type="button"
          @click="goBack"
          :disabled="paymentStatus === 'success'"
          class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Quay lại
        </button>
        <div class="space-x-4">
          <button
            v-if="paymentStatus === 'failed'"
            type="button"
            @click="retryPayment"
            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Thử lại
          </button>
          <button
            v-if="paymentStatus === 'success'"
            type="button"
            @click="goToResult"
            class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            Xem kết quả
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Thanh toán bảo hiểm'
})

const insuranceStore = useInsuranceStore()
const qrCodeUrl = ref('')
const paymentStatus = ref<'pending' | 'success' | 'failed'>('pending')
const timeRemaining = ref(15 * 60) // 15 phút
const paymentCheckInterval = ref<NodeJS.Timeout | null>(null)
const countdownInterval = ref<NodeJS.Timeout | null>(null)

// Kiểm tra nếu chưa có order ID
onMounted(() => {
  if (!insuranceStore.orderId) {
    navigateTo('/insurance/step2')
    return
  }
  
  // Khởi tạo thanh toán
  initializePayment()
  startPaymentCheck()
  startCountdown()
})

// Cleanup intervals khi component bị destroy
onUnmounted(() => {
  if (paymentCheckInterval.value) {
    clearInterval(paymentCheckInterval.value)
  }
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
  }
})

// Khởi tạo thanh toán và tạo QR code
const initializePayment = async () => {
  try {
    const { generatePaymentQR } = useInsuranceApi()

    if (insuranceStore.orderId) {
      const result = await generatePaymentQR(insuranceStore.orderId)

      if (result.success) {
        qrCodeUrl.value = result.qrUrl
        insuranceStore.setPaymentStatus('pending')
      } else {
        paymentStatus.value = 'failed'
      }
    }
  } catch (error) {
    console.error('Error initializing payment:', error)
    paymentStatus.value = 'failed'
  }
}

// Kiểm tra trạng thái thanh toán định kỳ
const startPaymentCheck = () => {
  paymentCheckInterval.value = setInterval(async () => {
    try {
      const { checkPaymentStatus } = useInsuranceApi()

      if (insuranceStore.orderId) {
        const result = await checkPaymentStatus(insuranceStore.orderId)

        if (result.success && result.status === 'success') {
          paymentStatus.value = 'success'
          insuranceStore.setPaymentStatus('success')

          // Clear intervals
          if (paymentCheckInterval.value) {
            clearInterval(paymentCheckInterval.value)
          }
          if (countdownInterval.value) {
            clearInterval(countdownInterval.value)
          }

          // Chuyển đến trang kết quả sau 2 giây
          setTimeout(() => {
            navigateTo('/insurance/step4')
          }, 2000)
        } else if (result.status === 'failed') {
          paymentStatus.value = 'failed'
          insuranceStore.setPaymentStatus('failed')

          // Clear intervals
          if (paymentCheckInterval.value) {
            clearInterval(paymentCheckInterval.value)
          }
          if (countdownInterval.value) {
            clearInterval(countdownInterval.value)
          }
        }
      }
    } catch (error) {
      console.error('Error checking payment status:', error)
    }
  }, 5000) // Check mỗi 5 giây
}

// Đếm ngược thời gian
const startCountdown = () => {
  countdownInterval.value = setInterval(() => {
    timeRemaining.value--
    
    if (timeRemaining.value <= 0) {
      // Hết thời gian thanh toán
      paymentStatus.value = 'failed'
      insuranceStore.setPaymentStatus('failed')
      
      if (countdownInterval.value) {
        clearInterval(countdownInterval.value)
      }
      if (paymentCheckInterval.value) {
        clearInterval(paymentCheckInterval.value)
      }
    }
  }, 1000)
}

// Format thời gian còn lại
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Quay lại bước 2
const goBack = () => {
  navigateTo('/insurance/step2')
}

// Thử lại thanh toán
const retryPayment = () => {
  paymentStatus.value = 'pending'
  timeRemaining.value = 15 * 60
  initializePayment()
  startPaymentCheck()
  startCountdown()
}

// Đi đến trang kết quả
const goToResult = () => {
  navigateTo('/insurance/step4')
}
</script>
